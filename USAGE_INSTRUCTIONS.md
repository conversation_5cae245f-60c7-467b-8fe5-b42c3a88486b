# 滤波器频率响应测试系统使用说明

## 系统概述

本系统实现了一个完整的滤波器频率响应测试功能，能够自动测量滤波器的幅频特性和相频特性。

## 硬件连接

### 信号连接图
```
DAC输出(PA4) ──→ 滤波器输入
                    │
                    ├──→ ADC1(PA1) [原始信号]
                    │
                    └──→ 滤波器 ──→ ADC3(PF7) [滤波后信号]
```

### 具体连接
1. **DAC输出**: PA4引脚 - 扫频信号源
2. **ADC1输入**: PA1引脚 - 接收原始扫频信号（滤波器输入）
3. **ADC3输入**: PF7引脚 - 接收滤波后信号（滤波器输出）
4. **串口**: 用于输出测试数据

## 操作步骤

### 1. 系统启动
- 上电后系统自动初始化
- LCD显示操作界面
- 串口输出"System Starting..."

### 2. 开始测试
- 按下LCD上的"ADC OFF"按钮
- 按钮变为"FREQ TEST"，颜色变绿
- 系统开始频率响应测试

### 3. 测试过程
- 系统自动扫频：0 Hz → 400.2 kHz，步进200 Hz
- 每个频率点：
  - DAC输出该频率正弦波（1.65V中心，1V峰峰值）
  - 等待信号稳定
  - ADC1和ADC3同时采集4096点数据
  - 进行FFT分析
  - 计算并输出频率响应

### 4. 数据输出
- 串口实时输出每个频率点的测试结果
- LCD显示当前测试进度

### 5. 测试完成
- 自动停止测试
- 按钮恢复为"ADC OFF"
- 总共测试2002个频率点

## 串口数据格式

### 输出格式
```
FREQ_RESPONSE: 频率(Hz), 增益(dB), 相位差(度), ADC1幅度, ADC3幅度, 增益比
```

### 示例数据
```
FREQ_RESPONSE: 1000.0, -3.010, -45.23, 0.123456, 0.087654, 0.710234
FREQ_RESPONSE: 1200.0, -3.521, -52.18, 0.118234, 0.081245, 0.687123
FREQ_RESPONSE: 1400.0, -4.123, -58.94, 0.112456, 0.074123, 0.659234
```

### 数据说明
- **频率**: 当前测试频率（Hz）
- **增益**: 20*log10(ADC3/ADC1)，单位dB
- **相位差**: ADC3相位 - ADC1相位，单位度
- **ADC1幅度**: 原始信号FFT幅度
- **ADC3幅度**: 滤波后信号FFT幅度
- **增益比**: ADC3幅度/ADC1幅度

## 数据处理建议

### 1. 数据收集
- 使用串口调试工具收集数据
- 建议波特率：115200
- 保存为文本文件

### 2. 数据分析
可以使用Excel、MATLAB或Python进行数据分析：

#### Excel处理
1. 导入文本数据
2. 分离各列数据
3. 绘制频率-增益曲线
4. 绘制频率-相位曲线

#### MATLAB处理
```matlab
% 读取数据
data = readtable('freq_response.txt');
freq = data.Frequency;
gain = data.Gain_dB;
phase = data.Phase_deg;

% 绘制幅频特性
subplot(2,1,1);
semilogx(freq, gain);
xlabel('Frequency (Hz)');
ylabel('Gain (dB)');
title('Magnitude Response');
grid on;

% 绘制相频特性
subplot(2,1,2);
semilogx(freq, phase);
xlabel('Frequency (Hz)');
ylabel('Phase (degrees)');
title('Phase Response');
grid on;
```

### 3. 滤波器参数提取
- **截止频率**: 找到-3dB点
- **滚降率**: 计算过渡带斜率
- **品质因数Q**: 对于带通滤波器
- **群延迟**: 相位对频率的导数

## 技术参数

- **频率范围**: 0 - 400.2 kHz
- **频率分辨率**: 200 Hz
- **测试点数**: 2002个
- **ADC分辨率**: 12位
- **采样率**: 815534 Hz
- **FFT长度**: 4096点
- **测试时间**: 约5分钟
- **动态范围**: >60dB

## 注意事项

### 1. 硬件注意事项
- 确保信号连接正确
- 注意阻抗匹配
- 避免信号过载（输入范围0-3.3V）
- 减少电磁干扰

### 2. 测试注意事项
- 测试前确保滤波器连接正确
- 避免在测试过程中断开连接
- 确保电源稳定
- 测试环境应相对安静

### 3. 数据处理注意事项
- 注意相位跳变（±180度）
- 低频段可能精度较低
- 高频段注意混叠效应
- 建议多次测试取平均值

## 故障排除

### 1. 无数据输出
- 检查串口连接
- 确认波特率设置
- 检查ADC连接

### 2. 数据异常
- 检查信号幅度是否合适
- 确认滤波器连接
- 检查接地连接

### 3. 测试中断
- 重新按下按钮重启测试
- 检查电源稳定性
- 确认系统未死机

## 扩展功能

系统支持以下扩展：
- 修改频率范围和步进
- 调整测试信号幅度
- 增加更多测试参数
- 支持不同类型的激励信号
