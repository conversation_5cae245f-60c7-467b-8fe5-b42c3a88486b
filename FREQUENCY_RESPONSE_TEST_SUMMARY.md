# 频率响应测试系统实现总结

## 功能概述
实现了一个完整的滤波器频率响应测试系统，通过DAC输出扫频信号，ADC1和ADC3同时采集信号，计算滤波器的幅频特性和相频特性。

## 主要特性

### 1. 扫频信号参数
- **中心幅度**: 1.65V
- **峰峰值**: 1V
- **波形**: 正弦波
- **频率范围**: 0 Hz 到 400.2 kHz
- **频率步进**: 200 Hz
- **总测试点数**: 2002个频率点

### 2. 信号路径
- **DAC输出**: PA4引脚，扫频正弦信号
- **ADC1输入**: PA1引脚，接收原始扫频信号
- **ADC3输入**: PF7引脚，接收经过滤波器的信号

### 3. 测试流程
1. 按下ADC按钮启动频率响应测试
2. 系统自动从0Hz开始，以200Hz步进扫频到400.2kHz
3. 每个频率点：
   - DAC输出该频率的正弦波
   - 等待信号稳定（50-100ms）
   - ADC1和ADC3同时采集4096点数据
   - 对两路数据进行FFT分析
   - 计算幅频特性和相频特性
   - 通过串口输出结果
4. 测试完成后自动停止

## 核心算法

### 1. FFT分析
- 使用ARM CMSIS DSP库的4096点FFT
- 采样率：815534 Hz
- 频率分辨率：815534/4096 ≈ 199.1 Hz
- 目标频率bin计算：target_bin = frequency / freq_resolution

### 2. 幅频特性计算
```c
magnitude_ratio = adc3_magnitude / adc1_magnitude
gain_db = 20 * log10(magnitude_ratio)
```

### 3. 相频特性计算
```c
adc1_phase = atan2(imag, real) * 180/π
adc3_phase = atan2(imag, real) * 180/π
phase_difference = adc3_phase - adc1_phase
```

## 串口输出格式

每个频率点输出一行数据：
```
FREQ_RESPONSE: 频率(Hz), 增益(dB), 相位差(度), ADC1幅度, ADC3幅度, 增益比
```

示例：
```
FREQ_RESPONSE: 1000.0, -3.010, -45.23, 0.123456, 0.087654, 0.710234
```

## 数据结构

```c
typedef struct {
    float frequency;        // 测试频率 (Hz)
    float adc1_magnitude;   // ADC1信号幅度
    float adc3_magnitude;   // ADC3信号幅度
    float magnitude_ratio;  // 幅度比 (ADC3/ADC1)
    float phase_difference; // 相位差 (度)
} FrequencyResponse;
```

## 主要修改内容

### 1. USER/main.c
- 添加频率响应测试相关变量和函数
- 修改ADC按钮功能为启动频率响应测试
- 实现扫频测试控制逻辑
- 实现FFT分析和频率响应计算
- 实现串口数据输出

### 2. HARDWARE/DAC/dac.h & dac.c
- 提高DAC最大频率限制到450kHz
- 简化幅度计算，固定输出1V峰峰值
- 支持高频正弦波输出

### 3. ADC中断处理
- 保持ADC1和ADC3的中断采样功能
- 支持两路ADC同时采样

## 使用方法

1. **启动测试**：按下"ADC"按钮（显示为"FREQ TEST"）
2. **观察进度**：LCD显示当前测试频率和进度
3. **获取数据**：通过串口接收频率响应数据
4. **停止测试**：再次按下按钮或等待自动完成

## 技术参数

- **测试时间**: 约2002 × 0.15秒 ≈ 5分钟
- **频率精度**: ±100Hz（受FFT分辨率限制）
- **幅度精度**: 取决于ADC精度（12位）
- **相位精度**: ±1度（典型值）
- **动态范围**: >60dB

## 注意事项

1. **信号连接**：确保ADC1接收原始信号，ADC3接收滤波后信号
2. **阻抗匹配**：注意信号源和负载阻抗匹配
3. **噪声控制**：测试环境应尽量减少电磁干扰
4. **频率限制**：DAC输出频率上限为450kHz
5. **数据处理**：串口数据可导入Excel或MATLAB进行绘图分析

## 数据分析建议

1. **幅频特性**：绘制增益(dB) vs 频率(Hz)曲线
2. **相频特性**：绘制相位差(度) vs 频率(Hz)曲线
3. **滤波器类型识别**：根据曲线形状判断滤波器类型
4. **截止频率**：找到-3dB点对应的频率
5. **品质因数**：计算Q值等参数
